{"original": {"content": "test(() => {\n    it(() => {\n        console.log(1);\n    })\n\n    it(() => {\n    })\n});\n\ntest(() => {\n    it(() => {\n        console.log(1);\n    })\n\n    it(() => {\n    })\n});", "fileName": "./1.tst"}, "modified": {"content": "test(() => {\n    it(() => {\n        console.log(1);\n    })\n\n    it(() => {\n2        console.log(2);\n    })\n\n    it(() => {\n    \n    })\n});\n\ntest(() => {\n    it(() => {\n        console.log(1);\n    })\n\n    it(() => {\n    })\n\n    it(() => {\n    \n    })\n});", "fileName": "./2.tst"}, "diffs": [{"originalRange": "[7,7)", "modifiedRange": "[7,12)", "innerChanges": null}, {"originalRange": "[17,17)", "modifiedRange": "[22,26)", "innerChanges": null}]}