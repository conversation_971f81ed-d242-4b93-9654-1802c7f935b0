/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
export class ViewContext {
    constructor(notebookOptions, eventDispatcher, getBaseCellEditorOptions) {
        this.notebookOptions = notebookOptions;
        this.eventDispatcher = eventDispatcher;
        this.getBaseCellEditorOptions = getBaseCellEditorOptions;
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidmlld0NvbnRleHQuanMiLCJzb3VyY2VSb290IjoiZmlsZTovLy9DOi9Vc2Vycy9CaGF3ZXNoL0Rlc2t0b3AvdGVzdGluZ19wdXJwb3Nlcy90ZXN0aW5nX3B1cnBvc2VzL0Zsb3cvc3JjLyIsInNvdXJjZXMiOlsidnMvd29ya2JlbmNoL2NvbnRyaWIvbm90ZWJvb2svYnJvd3Nlci92aWV3TW9kZWwvdmlld0NvbnRleHQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7OztnR0FHZ0c7QUFNaEcsTUFBTSxPQUFPLFdBQVc7SUFDdkIsWUFDVSxlQUFnQyxFQUNoQyxlQUF3QyxFQUN4Qyx3QkFBc0U7UUFGdEUsb0JBQWUsR0FBZixlQUFlLENBQWlCO1FBQ2hDLG9CQUFlLEdBQWYsZUFBZSxDQUF5QjtRQUN4Qyw2QkFBd0IsR0FBeEIsd0JBQXdCLENBQThDO0lBRWhGLENBQUM7Q0FDRCJ9