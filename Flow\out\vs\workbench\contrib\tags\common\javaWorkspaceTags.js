/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
export const GradleDependencyLooseRegex = /group\s*:\s*[\'\"](.*?)[\'\"]\s*,\s*name\s*:\s*[\'\"](.*?)[\'\"]\s*,\s*version\s*:\s*[\'\"](.*?)[\'\"]/g;
export const GradleDependencyCompactRegex = /[\'\"]([^\'\"\s]*?)\:([^\'\"\s]*?)\:([^\'\"\s]*?)[\'\"]/g;
export const MavenDependenciesRegex = /<dependencies>([\s\S]*?)<\/dependencies>/g;
export const MavenDependencyRegex = /<dependency>([\s\S]*?)<\/dependency>/g;
export const MavenGroupIdRegex = /<groupId>([\s\S]*?)<\/groupId>/;
export const MavenArtifactIdRegex = /<artifactId>([\s\S]*?)<\/artifactId>/;
export const JavaLibrariesToLookFor = [
    // azure mgmt sdk
    { 'predicate': (groupId, artifactId) => groupId === 'com.microsoft.azure' && artifactId === 'azure', 'tag': 'azure' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.microsoft.azure' && artifactId.startsWith('azure-mgmt-'), 'tag': 'azure' },
    { 'predicate': (groupId, artifactId) => groupId.startsWith('com.microsoft.azure') && artifactId.startsWith('azure-mgmt-'), 'tag': 'azure' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure.resourcemanager' && artifactId.startsWith('azure-resourcemanager'), 'tag': 'azure' }, // azure track2 sdk
    // java ee
    { 'predicate': (groupId, artifactId) => groupId === 'javax' && artifactId === 'javaee-api', 'tag': 'javaee' },
    { 'predicate': (groupId, artifactId) => groupId === 'javax.xml.bind' && artifactId === 'jaxb-api', 'tag': 'javaee' },
    // jdbc
    { 'predicate': (groupId, artifactId) => groupId === 'mysql' && artifactId === 'mysql-connector-java', 'tag': 'jdbc' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.microsoft.sqlserver' && artifactId === 'mssql-jdbc', 'tag': 'jdbc' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.oracle.database.jdbc' && artifactId.startsWith('ojdbc'), 'tag': 'jdbc' },
    // jpa
    { 'predicate': (groupId, artifactId) => groupId === 'org.hibernate', 'tag': 'jpa' },
    { 'predicate': (groupId, artifactId) => groupId === 'org.eclipse.persistence' && artifactId === 'eclipselink', 'tag': 'jpa' },
    // lombok
    { 'predicate': (groupId, artifactId) => groupId === 'org.projectlombok', 'tag': 'lombok' },
    // redis
    { 'predicate': (groupId, artifactId) => groupId === 'org.springframework.data' && artifactId === 'spring-data-redis', 'tag': 'redis' },
    { 'predicate': (groupId, artifactId) => groupId === 'redis.clients' && artifactId === 'jedis', 'tag': 'redis' },
    { 'predicate': (groupId, artifactId) => groupId === 'org.redisson', 'tag': 'redis' },
    { 'predicate': (groupId, artifactId) => groupId === 'io.lettuce' && artifactId === 'lettuce-core', 'tag': 'redis' },
    // spring boot
    { 'predicate': (groupId, artifactId) => groupId === 'org.springframework.boot', 'tag': 'springboot' },
    // sql
    { 'predicate': (groupId, artifactId) => groupId === 'org.jooq', 'tag': 'sql' },
    { 'predicate': (groupId, artifactId) => groupId === 'org.mybatis', 'tag': 'sql' },
    // unit test
    { 'predicate': (groupId, artifactId) => groupId === 'org.junit.jupiter' && artifactId === 'junit-jupiter-api', 'tag': 'unitTest' },
    { 'predicate': (groupId, artifactId) => groupId === 'junit' && artifactId === 'junit', 'tag': 'unitTest' },
    { 'predicate': (groupId, artifactId) => groupId === 'org.testng' && artifactId === 'testng', 'tag': 'unitTest' },
    // cosmos
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId.includes('cosmos'), 'tag': 'azure-cosmos' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure.spring' && artifactId.includes('cosmos'), 'tag': 'azure-cosmos' },
    // storage account
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId.includes('azure-storage'), 'tag': 'azure-storage' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure.spring' && artifactId.includes('storage'), 'tag': 'azure-storage' },
    // service bus
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-messaging-servicebus', 'tag': 'azure-servicebus' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure.spring' && artifactId.includes('servicebus'), 'tag': 'azure-servicebus' },
    // event hubs
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId.startsWith('azure-messaging-eventhubs'), 'tag': 'azure-eventhubs' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure.spring' && artifactId.includes('eventhubs'), 'tag': 'azure-eventhubs' },
    // ai related libraries
    { 'predicate': (groupId, artifactId) => groupId === 'dev.langchain4j', 'tag': 'langchain4j' },
    { 'predicate': (groupId, artifactId) => groupId === 'io.springboot.ai', 'tag': 'springboot-ai' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.microsoft.semantic-kernel', 'tag': 'semantic-kernel' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-anomalydetector', 'tag': 'azure-ai-anomalydetector' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-formrecognizer', 'tag': 'azure-ai-formrecognizer' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-documentintelligence', 'tag': 'azure-ai-documentintelligence' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-translation-document', 'tag': 'azure-ai-translation-document' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-personalizer', 'tag': 'azure-ai-personalizer' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-translation-text', 'tag': 'azure-ai-translation-text' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-contentsafety', 'tag': 'azure-ai-contentsafety' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-vision-imageanalysis', 'tag': 'azure-ai-vision-imageanalysis' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-textanalytics', 'tag': 'azure-ai-textanalytics' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-search-documents', 'tag': 'azure-search-documents' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-documenttranslator', 'tag': 'azure-ai-documenttranslator' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-vision-face', 'tag': 'azure-ai-vision-face' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-openai-assistants', 'tag': 'azure-ai-openai-assistants' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.microsoft.azure.cognitiveservices', 'tag': 'azure-cognitiveservices' },
    { 'predicate': (groupId, artifactId) => groupId === 'com.microsoft.cognitiveservices.speech', 'tag': 'azure-cognitiveservices-speech' },
    // open ai
    { 'predicate': (groupId, artifactId) => groupId === 'com.theokanning.openai-gpt3-java', 'tag': 'openai' },
    // azure open ai
    { 'predicate': (groupId, artifactId) => groupId === 'com.azure' && artifactId === 'azure-ai-openai', 'tag': 'azure-openai' },
    // Azure Functions
    { 'predicate': (groupId, artifactId) => groupId === 'com.microsoft.azure.functions' && artifactId === 'azure-functions-java-library', 'tag': 'azure-functions' },
    // quarkus
    { 'predicate': (groupId, artifactId) => groupId === 'io.quarkus', 'tag': 'quarkus' },
    // microprofile
    { 'predicate': (groupId, artifactId) => groupId.startsWith('org.eclipse.microprofile'), 'tag': 'microprofile' },
    // micronaut
    { 'predicate': (groupId, artifactId) => groupId === 'io.micronaut', 'tag': 'micronaut' },
    // GraalVM
    { 'predicate': (groupId, artifactId) => groupId.startsWith('org.graalvm'), 'tag': 'graalvm' }
];
//# sourceMappingURL=data:application/json;base64,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