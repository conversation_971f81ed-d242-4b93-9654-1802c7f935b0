/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { Codicon } from '../../../../base/common/codicons.js';
import { localize } from '../../../../nls.js';
import { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';
export const DEFAULT_ICON = registerIcon('settings-view-bar-icon', Codicon.settingsGear, localize('settingsViewBarIcon', "Settings icon in the view bar."));
export const ICONS = [
    /* Default */
    DEFAULT_ICON,
    /* hardware/devices */
    Codicon.vm,
    Codicon.server,
    Codicon.recordKeys,
    Codicon.deviceMobile,
    Codicon.watch,
    Codicon.vr,
    Codicon.piano,
    /* languages */
    Codicon.ruby,
    Codicon.code,
    Codicon.coffee,
    Codicon.snake,
    /* project types */
    Codicon.project,
    Codicon.window,
    Codicon.library,
    Codicon.extensions,
    Codicon.terminal,
    Codicon.terminalDebian,
    Codicon.terminalLinux,
    Codicon.terminalUbuntu,
    Codicon.beaker,
    Codicon.package,
    Codicon.cloud,
    Codicon.book,
    Codicon.globe,
    Codicon.database,
    Codicon.notebook,
    Codicon.robot,
    Codicon.game,
    Codicon.chip,
    Codicon.music,
    Codicon.remoteExplorer,
    Codicon.github,
    Codicon.azure,
    Codicon.vscode,
    Codicon.copilot,
    /* misc */
    Codicon.gift,
    Codicon.send,
    Codicon.bookmark,
    Codicon.briefcase,
    Codicon.megaphone,
    Codicon.comment,
    Codicon.telescope,
    Codicon.creditCard,
    Codicon.map,
    Codicon.deviceCameraVideo,
    Codicon.unmute,
    Codicon.law,
    Codicon.graphLine,
    Codicon.heart,
    Codicon.home,
    Codicon.inbox,
    Codicon.mortarBoard,
    Codicon.rocket,
    Codicon.magnet,
    Codicon.lock,
    Codicon.milestone,
    Codicon.tag,
    Codicon.pulse,
    Codicon.radioTower,
    Codicon.smiley,
    Codicon.zap,
    Codicon.squirrel,
    Codicon.symbolColor,
    Codicon.mail,
    Codicon.key,
    Codicon.pieChart,
    Codicon.organization,
    Codicon.preview,
    Codicon.wand,
    Codicon.starEmpty,
    Codicon.lightbulb,
    Codicon.symbolRuler,
    Codicon.dashboard,
    Codicon.calendar,
    Codicon.shield,
    Codicon.verified,
    Codicon.debug,
    Codicon.flame,
    Codicon.compass,
    Codicon.paintcan,
    Codicon.archive,
    Codicon.mic,
    Codicon.jersey,
];
//# sourceMappingURL=data:application/json;base64,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