{
  parts: [
    {
      range: {
        start: 0,
        endExclusive: 6
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 1,
        endLineNumber: 1,
        endColumn: 7
      },
      agent: {
        id: "agent",
        name: "agent",
        extensionId: {
          value: "nullExtensionDescription",
          _lower: "nullextensiondescription"
        },
        publisherDisplayName: "",
        extensionDisplayName: "",
        extensionPublisherId: "",
        locations: [ "panel" ],
        metadata: {  },
        slashCommands: [
          {
            name: "subCommand",
            description: ""
          }
        ],
        disambiguation: [  ]
      },
      kind: "agent"
    },
    {
      range: {
        start: 6,
        endExclusive: 7
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 7,
        endLineNumber: 1,
        endColumn: 8
      },
      text: " ",
      kind: "text"
    },
    {
      range: {
        start: 7,
        endExclusive: 18
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 8,
        endLineNumber: 1,
        endColumn: 19
      },
      command: {
        name: "subCommand",
        description: ""
      },
      kind: "subcommand"
    },
    {
      range: {
        start: 18,
        endExclusive: 35
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 19,
        endLineNumber: 1,
        endColumn: 36
      },
      text: " Please do thanks",
      kind: "text"
    }
  ],
  text: "@agent /subCommand Please do thanks"
}