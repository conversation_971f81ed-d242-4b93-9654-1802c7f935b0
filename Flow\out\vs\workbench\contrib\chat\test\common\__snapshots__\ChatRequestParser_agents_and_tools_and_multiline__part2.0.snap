{
  parts: [
    {
      range: {
        start: 0,
        endExclusive: 6
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 1,
        endLineNumber: 1,
        endColumn: 7
      },
      agent: {
        id: "agent",
        name: "agent",
        extensionId: {
          value: "nullExtensionDescription",
          _lower: "nullextensiondescription"
        },
        publisherDisplayName: "",
        extensionDisplayName: "",
        extensionPublisherId: "",
        locations: [ "panel" ],
        metadata: {  },
        slashCommands: [
          {
            name: "subCommand",
            description: ""
          }
        ],
        disambiguation: [  ]
      },
      kind: "agent"
    },
    {
      range: {
        start: 6,
        endExclusive: 35
      },
      editorRange: {
        startLineNumber: 1,
        startColumn: 7,
        endLineNumber: 2,
        endColumn: 21
      },
      text: " Please \ndo /subCommand with ",
      kind: "text"
    },
    {
      range: {
        start: 35,
        endExclusive: 45
      },
      editorRange: {
        startLineNumber: 2,
        startColumn: 21,
        endLineNumber: 2,
        endColumn: 31
      },
      toolName: "selection",
      toolId: "get_selection",
      displayName: "",
      icon: undefined,
      kind: "tool"
    },
    {
      range: {
        start: 45,
        endExclusive: 50
      },
      editorRange: {
        startLineNumber: 2,
        startColumn: 31,
        endLineNumber: 3,
        endColumn: 5
      },
      text: "\nand ",
      kind: "text"
    },
    {
      range: {
        start: 50,
        endExclusive: 63
      },
      editorRange: {
        startLineNumber: 3,
        startColumn: 5,
        endLineNumber: 3,
        endColumn: 18
      },
      toolName: "debugConsole",
      toolId: "get_debugConsole",
      displayName: "",
      icon: undefined,
      kind: "tool"
    }
  ],
  text: "@agent Please \ndo /subCommand with #selection\nand #debugConsole"
}