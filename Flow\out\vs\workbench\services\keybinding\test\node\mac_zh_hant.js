/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
'use strict';

define({
	KeyA: {
		value: 'ㄇ',
		valueIsDeadKey: false,
		withShift: 'A',
		withShiftIsDeadKey: false,
		withAltGr: 'a',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'A',
		withShiftAltGrIsDeadKey: false
	},
	KeyB: {
		value: 'ㄖ',
		valueIsDeadKey: false,
		withShift: 'B',
		withShiftIsDeadKey: false,
		withAltGr: 'b',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'B',
		withShiftAltGrIsDeadKey: false
	},
	KeyC: {
		value: 'ㄏ',
		valueIsDeadKey: false,
		withShift: 'C',
		withShiftIsDeadKey: false,
		withAltGr: 'c',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'C',
		withShiftAltGrIsDeadKey: false
	},
	KeyD: {
		value: 'ㄎ',
		valueIsDeadKey: false,
		withShift: 'D',
		withShiftIsDeadKey: false,
		withAltGr: 'd',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'D',
		withShiftAltGrIsDeadKey: false
	},
	KeyE: {
		value: 'ㄍ',
		valueIsDeadKey: false,
		withShift: 'E',
		withShiftIsDeadKey: false,
		withAltGr: 'e',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'E',
		withShiftAltGrIsDeadKey: false
	},
	KeyF: {
		value: 'ㄑ',
		valueIsDeadKey: false,
		withShift: 'F',
		withShiftIsDeadKey: false,
		withAltGr: 'f',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'F',
		withShiftAltGrIsDeadKey: false
	},
	KeyG: {
		value: 'ㄕ',
		valueIsDeadKey: false,
		withShift: 'G',
		withShiftIsDeadKey: false,
		withAltGr: 'g',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'G',
		withShiftAltGrIsDeadKey: false
	},
	KeyH: {
		value: 'ㄘ',
		valueIsDeadKey: false,
		withShift: 'H',
		withShiftIsDeadKey: false,
		withAltGr: 'h',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'H',
		withShiftAltGrIsDeadKey: false
	},
	KeyI: {
		value: 'ㄛ',
		valueIsDeadKey: false,
		withShift: 'I',
		withShiftIsDeadKey: false,
		withAltGr: 'i',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'I',
		withShiftAltGrIsDeadKey: false
	},
	KeyJ: {
		value: 'ㄨ',
		valueIsDeadKey: false,
		withShift: 'J',
		withShiftIsDeadKey: false,
		withAltGr: 'j',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'J',
		withShiftAltGrIsDeadKey: false
	},
	KeyK: {
		value: 'ㄜ',
		valueIsDeadKey: false,
		withShift: 'K',
		withShiftIsDeadKey: false,
		withAltGr: 'k',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'K',
		withShiftAltGrIsDeadKey: false
	},
	KeyL: {
		value: 'ㄠ',
		valueIsDeadKey: false,
		withShift: 'L',
		withShiftIsDeadKey: false,
		withAltGr: 'l',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'L',
		withShiftAltGrIsDeadKey: false
	},
	KeyM: {
		value: 'ㄩ',
		valueIsDeadKey: false,
		withShift: 'M',
		withShiftIsDeadKey: false,
		withAltGr: 'm',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'M',
		withShiftAltGrIsDeadKey: false
	},
	KeyN: {
		value: 'ㄙ',
		valueIsDeadKey: false,
		withShift: 'N',
		withShiftIsDeadKey: false,
		withAltGr: 'n',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'N',
		withShiftAltGrIsDeadKey: false
	},
	KeyO: {
		value: 'ㄟ',
		valueIsDeadKey: false,
		withShift: 'O',
		withShiftIsDeadKey: false,
		withAltGr: 'o',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'O',
		withShiftAltGrIsDeadKey: false
	},
	KeyP: {
		value: 'ㄣ',
		valueIsDeadKey: false,
		withShift: 'P',
		withShiftIsDeadKey: false,
		withAltGr: 'p',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'P',
		withShiftAltGrIsDeadKey: false
	},
	KeyQ: {
		value: 'ㄆ',
		valueIsDeadKey: false,
		withShift: 'Q',
		withShiftIsDeadKey: false,
		withAltGr: 'q',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'Q',
		withShiftAltGrIsDeadKey: false
	},
	KeyR: {
		value: 'ㄐ',
		valueIsDeadKey: false,
		withShift: 'R',
		withShiftIsDeadKey: false,
		withAltGr: 'r',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'R',
		withShiftAltGrIsDeadKey: false
	},
	KeyS: {
		value: 'ㄋ',
		valueIsDeadKey: false,
		withShift: 'S',
		withShiftIsDeadKey: false,
		withAltGr: 's',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'S',
		withShiftAltGrIsDeadKey: false
	},
	KeyT: {
		value: 'ㄔ',
		valueIsDeadKey: false,
		withShift: 'T',
		withShiftIsDeadKey: false,
		withAltGr: 't',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'T',
		withShiftAltGrIsDeadKey: false
	},
	KeyU: {
		value: 'ㄧ',
		valueIsDeadKey: false,
		withShift: 'U',
		withShiftIsDeadKey: false,
		withAltGr: 'u',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'U',
		withShiftAltGrIsDeadKey: false
	},
	KeyV: {
		value: 'ㄒ',
		valueIsDeadKey: false,
		withShift: 'V',
		withShiftIsDeadKey: false,
		withAltGr: 'v',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'V',
		withShiftAltGrIsDeadKey: false
	},
	KeyW: {
		value: 'ㄊ',
		valueIsDeadKey: false,
		withShift: 'W',
		withShiftIsDeadKey: false,
		withAltGr: 'w',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'W',
		withShiftAltGrIsDeadKey: false
	},
	KeyX: {
		value: 'ㄌ',
		valueIsDeadKey: false,
		withShift: 'X',
		withShiftIsDeadKey: false,
		withAltGr: 'x',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'X',
		withShiftAltGrIsDeadKey: false
	},
	KeyY: {
		value: 'ㄗ',
		valueIsDeadKey: false,
		withShift: 'Y',
		withShiftIsDeadKey: false,
		withAltGr: 'y',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'Y',
		withShiftAltGrIsDeadKey: false
	},
	KeyZ: {
		value: 'ㄈ',
		valueIsDeadKey: false,
		withShift: 'Z',
		withShiftIsDeadKey: false,
		withAltGr: 'z',
		withAltGrIsDeadKey: false,
		withShiftAltGr: 'Z',
		withShiftAltGrIsDeadKey: false
	},
	Digit1: {
		value: 'ㄅ',
		valueIsDeadKey: false,
		withShift: '!',
		withShiftIsDeadKey: false,
		withAltGr: '1',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '!',
		withShiftAltGrIsDeadKey: false
	},
	Digit2: {
		value: 'ㄉ',
		valueIsDeadKey: false,
		withShift: '@',
		withShiftIsDeadKey: false,
		withAltGr: '2',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '@',
		withShiftAltGrIsDeadKey: false
	},
	Digit3: {
		value: 'ˇ',
		valueIsDeadKey: false,
		withShift: '#',
		withShiftIsDeadKey: false,
		withAltGr: '3',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '#',
		withShiftAltGrIsDeadKey: false
	},
	Digit4: {
		value: 'ˋ',
		valueIsDeadKey: false,
		withShift: '$',
		withShiftIsDeadKey: false,
		withAltGr: '4',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '$',
		withShiftAltGrIsDeadKey: false
	},
	Digit5: {
		value: 'ㄓ',
		valueIsDeadKey: false,
		withShift: '%',
		withShiftIsDeadKey: false,
		withAltGr: '5',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '%',
		withShiftAltGrIsDeadKey: false
	},
	Digit6: {
		value: 'ˊ',
		valueIsDeadKey: false,
		withShift: '^',
		withShiftIsDeadKey: false,
		withAltGr: '6',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '^',
		withShiftAltGrIsDeadKey: false
	},
	Digit7: {
		value: '˙',
		valueIsDeadKey: false,
		withShift: '&',
		withShiftIsDeadKey: false,
		withAltGr: '7',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '&',
		withShiftAltGrIsDeadKey: false
	},
	Digit8: {
		value: 'ㄚ',
		valueIsDeadKey: false,
		withShift: '*',
		withShiftIsDeadKey: false,
		withAltGr: '8',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '*',
		withShiftAltGrIsDeadKey: false
	},
	Digit9: {
		value: 'ㄞ',
		valueIsDeadKey: false,
		withShift: '(',
		withShiftIsDeadKey: false,
		withAltGr: '9',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '(',
		withShiftAltGrIsDeadKey: false
	},
	Digit0: {
		value: 'ㄢ',
		valueIsDeadKey: false,
		withShift: ')',
		withShiftIsDeadKey: false,
		withAltGr: '0',
		withAltGrIsDeadKey: false,
		withShiftAltGr: ')',
		withShiftAltGrIsDeadKey: false
	},
	Enter: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	Escape: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '‍',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '‌',
		withShiftAltGrIsDeadKey: false
	},
	Backspace: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	Tab: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	Space: {
		value: ' ',
		valueIsDeadKey: false,
		withShift: ' ',
		withShiftIsDeadKey: false,
		withAltGr: ' ',
		withAltGrIsDeadKey: false,
		withShiftAltGr: ' ',
		withShiftAltGrIsDeadKey: false
	},
	Minus: {
		value: 'ㄦ',
		valueIsDeadKey: false,
		withShift: '_',
		withShiftIsDeadKey: false,
		withAltGr: '—',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '_',
		withShiftAltGrIsDeadKey: false
	},
	Equal: {
		value: '=',
		valueIsDeadKey: false,
		withShift: '+',
		withShiftIsDeadKey: false,
		withAltGr: '=',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '+',
		withShiftAltGrIsDeadKey: false
	},
	BracketLeft: {
		value: '「',
		valueIsDeadKey: false,
		withShift: '『',
		withShiftIsDeadKey: false,
		withAltGr: '[',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '{',
		withShiftAltGrIsDeadKey: false
	},
	BracketRight: {
		value: '」',
		valueIsDeadKey: false,
		withShift: '』',
		withShiftIsDeadKey: false,
		withAltGr: ']',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '}',
		withShiftAltGrIsDeadKey: false
	},
	Backslash: {
		value: '、',
		valueIsDeadKey: false,
		withShift: '|',
		withShiftIsDeadKey: false,
		withAltGr: '\\',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '|',
		withShiftAltGrIsDeadKey: false
	},
	Semicolon: {
		value: 'ㄤ',
		valueIsDeadKey: false,
		withShift: ':',
		withShiftIsDeadKey: false,
		withAltGr: ';',
		withAltGrIsDeadKey: false,
		withShiftAltGr: ':',
		withShiftAltGrIsDeadKey: false
	},
	Quote: {
		value: '\'',
		valueIsDeadKey: false,
		withShift: '"',
		withShiftIsDeadKey: false,
		withAltGr: '\'',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '"',
		withShiftAltGrIsDeadKey: false
	},
	Backquote: {
		value: '·',
		valueIsDeadKey: false,
		withShift: '~',
		withShiftIsDeadKey: false,
		withAltGr: '`',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '~',
		withShiftAltGrIsDeadKey: false
	},
	Comma: {
		value: 'ㄝ',
		valueIsDeadKey: false,
		withShift: '，',
		withShiftIsDeadKey: false,
		withAltGr: ',',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '<',
		withShiftAltGrIsDeadKey: false
	},
	Period: {
		value: 'ㄡ',
		valueIsDeadKey: false,
		withShift: '。',
		withShiftIsDeadKey: false,
		withAltGr: '.',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '>',
		withShiftAltGrIsDeadKey: false
	},
	Slash: {
		value: 'ㄥ',
		valueIsDeadKey: false,
		withShift: '?',
		withShiftIsDeadKey: false,
		withAltGr: '/',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '?',
		withShiftAltGrIsDeadKey: false
	},
	CapsLock: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F1: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F2: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F3: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F4: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F5: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F6: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F7: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F8: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F9: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F10: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F11: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F12: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	Insert: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	Home: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	PageUp: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	Delete: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	End: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	PageDown: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	ArrowRight: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	ArrowLeft: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	ArrowDown: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	ArrowUp: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	NumLock: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	NumpadDivide: {
		value: '/',
		valueIsDeadKey: false,
		withShift: '/',
		withShiftIsDeadKey: false,
		withAltGr: '/',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '/',
		withShiftAltGrIsDeadKey: false
	},
	NumpadMultiply: {
		value: '*',
		valueIsDeadKey: false,
		withShift: '*',
		withShiftIsDeadKey: false,
		withAltGr: '*',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '*',
		withShiftAltGrIsDeadKey: false
	},
	NumpadSubtract: {
		value: '-',
		valueIsDeadKey: false,
		withShift: '-',
		withShiftIsDeadKey: false,
		withAltGr: '-',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '-',
		withShiftAltGrIsDeadKey: false
	},
	NumpadAdd: {
		value: '+',
		valueIsDeadKey: false,
		withShift: '+',
		withShiftIsDeadKey: false,
		withAltGr: '+',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '+',
		withShiftAltGrIsDeadKey: false
	},
	NumpadEnter: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	Numpad1: {
		value: '1',
		valueIsDeadKey: false,
		withShift: '1',
		withShiftIsDeadKey: false,
		withAltGr: '1',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '1',
		withShiftAltGrIsDeadKey: false
	},
	Numpad2: {
		value: '2',
		valueIsDeadKey: false,
		withShift: '2',
		withShiftIsDeadKey: false,
		withAltGr: '2',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '2',
		withShiftAltGrIsDeadKey: false
	},
	Numpad3: {
		value: '3',
		valueIsDeadKey: false,
		withShift: '3',
		withShiftIsDeadKey: false,
		withAltGr: '3',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '3',
		withShiftAltGrIsDeadKey: false
	},
	Numpad4: {
		value: '4',
		valueIsDeadKey: false,
		withShift: '4',
		withShiftIsDeadKey: false,
		withAltGr: '4',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '4',
		withShiftAltGrIsDeadKey: false
	},
	Numpad5: {
		value: '5',
		valueIsDeadKey: false,
		withShift: '5',
		withShiftIsDeadKey: false,
		withAltGr: '5',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '5',
		withShiftAltGrIsDeadKey: false
	},
	Numpad6: {
		value: '6',
		valueIsDeadKey: false,
		withShift: '6',
		withShiftIsDeadKey: false,
		withAltGr: '6',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '6',
		withShiftAltGrIsDeadKey: false
	},
	Numpad7: {
		value: '7',
		valueIsDeadKey: false,
		withShift: '7',
		withShiftIsDeadKey: false,
		withAltGr: '7',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '7',
		withShiftAltGrIsDeadKey: false
	},
	Numpad8: {
		value: '8',
		valueIsDeadKey: false,
		withShift: '8',
		withShiftIsDeadKey: false,
		withAltGr: '8',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '8',
		withShiftAltGrIsDeadKey: false
	},
	Numpad9: {
		value: '9',
		valueIsDeadKey: false,
		withShift: '9',
		withShiftIsDeadKey: false,
		withAltGr: '9',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '9',
		withShiftAltGrIsDeadKey: false
	},
	Numpad0: {
		value: '0',
		valueIsDeadKey: false,
		withShift: '0',
		withShiftIsDeadKey: false,
		withAltGr: '0',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '0',
		withShiftAltGrIsDeadKey: false
	},
	NumpadDecimal: {
		value: '.',
		valueIsDeadKey: false,
		withShift: '.',
		withShiftIsDeadKey: false,
		withAltGr: '.',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '.',
		withShiftAltGrIsDeadKey: false
	},
	IntlBackslash: {
		value: '§',
		valueIsDeadKey: false,
		withShift: '±',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	ContextMenu: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	NumpadEqual: {
		value: '=',
		valueIsDeadKey: false,
		withShift: '=',
		withShiftIsDeadKey: false,
		withAltGr: '=',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '=',
		withShiftAltGrIsDeadKey: false
	},
	F13: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F14: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F15: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F16: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F17: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F18: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F19: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	F20: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	AudioVolumeMute: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	AudioVolumeUp: {
		value: '',
		valueIsDeadKey: false,
		withShift: '=',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '=',
		withShiftAltGrIsDeadKey: false
	},
	AudioVolumeDown: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	NumpadComma: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	IntlRo: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	KanaMode: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	IntlYen: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	ControlLeft: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	ShiftLeft: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	AltLeft: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	MetaLeft: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	ControlRight: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	ShiftRight: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	AltRight: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	},
	MetaRight: {
		value: '',
		valueIsDeadKey: false,
		withShift: '',
		withShiftIsDeadKey: false,
		withAltGr: '',
		withAltGrIsDeadKey: false,
		withShiftAltGr: '',
		withShiftAltGrIsDeadKey: false
	}
});

//# sourceURL=file:///C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/services/keybinding/test/node/mac_zh_hant.js