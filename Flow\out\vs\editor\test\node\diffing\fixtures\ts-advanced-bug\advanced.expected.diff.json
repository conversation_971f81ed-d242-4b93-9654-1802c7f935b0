{"original": {"content": "function compileProgram(): ExitStatus {\n    // First get any syntactic errors. \n    var diagnostics = program.getSyntacticDiagnostics();\n    reportDiagnostics(diagnostics);\n\n    // If we didn't have any syntactic errors, then also try getting the global and\n    // semantic errors.\n    if (diagnostics.length === 0) ", "fileName": "./1.tst"}, "modified": {"content": "function compileProgram(): ExitStatus {\n    let diagnostics: Diagnostic[];\n    \n    // First get and report any syntactic errors.\n    diagnostics = program.getSyntacticDiagnostics();\n\n    // If we didn't have any syntactic errors, then also try getting the global and\n    // semantic errors.\n    if (diagnostics.length === 0) {", "fileName": "./2.tst"}, "diffs": [{"originalRange": "[2,5)", "modifiedRange": "[2,6)", "innerChanges": [{"originalRange": "[2,1 -> 2,1]", "modifiedRange": "[2,1 -> 4,1]"}, {"originalRange": "[2,17 -> 2,17]", "modifiedRange": "[4,17 -> 4,28]"}, {"originalRange": "[2,39 -> 2,40 EOL]", "modifiedRange": "[4,50 -> 4,50 EOL]"}, {"originalRange": "[3,5 -> 3,9]", "modifiedRange": "[5,5 -> 5,5]"}, {"originalRange": "[4,1 -> 5,1 EOL]", "modifiedRange": "[6,1 -> 6,1 EOL]"}]}, {"originalRange": "[8,9)", "modifiedRange": "[9,10)", "innerChanges": [{"originalRange": "[8,35 -> 8,35 EOL]", "modifiedRange": "[9,35 -> 9,36 EOL]"}]}]}