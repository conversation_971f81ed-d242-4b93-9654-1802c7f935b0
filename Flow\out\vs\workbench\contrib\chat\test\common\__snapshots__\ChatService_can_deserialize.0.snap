{
  requesterUsername: "test",
  requesterAvatarIconUri: undefined,
  responderUsername: "",
  responderAvatarIconUri: undefined,
  initialLocation: "panel",
  requests: [
    {
      requestId: undefined,
      message: {
        text: "@ChatProviderWithUsedContext test request",
        parts: [
          {
            range: {
              start: 0,
              endExclusive: 28
            },
            editorRange: {
              startLineNumber: 1,
              startColumn: 1,
              endLineNumber: 1,
              endColumn: 29
            },
            agent: {
              name: "ChatProviderWithUsedContext",
              id: "ChatProviderWithUsedContext",
              extensionId: {
                value: "nullExtensionDescription",
                _lower: "nullextensiondescription"
              },
              extensionPublisherId: "",
              publisherDisplayName: "",
              extensionDisplayName: "",
              locations: [ "panel" ],
              metadata: {  },
              slashCommands: [  ],
              disambiguation: [  ]
            },
            kind: "agent"
          },
          {
            range: {
              start: 28,
              endExclusive: 41
            },
            editorRange: {
              startLineNumber: 1,
              startColumn: 29,
              endLineNumber: 1,
              endColumn: 42
            },
            text: " test request",
            kind: "text"
          }
        ]
      },
      variableData: { variables: [  ] },
      response: [  ],
      responseId: undefined,
      shouldBeRemovedOnSend: undefined,
      result: { metadata: { metadataKey: "value" } },
      followups: undefined,
      isCanceled: false,
      vote: undefined,
      voteDownReason: undefined,
      agent: {
        name: "ChatProviderWithUsedContext",
        id: "ChatProviderWithUsedContext",
        extensionId: {
          value: "nullExtensionDescription",
          _lower: "nullextensiondescription"
        },
        extensionPublisherId: "",
        publisherDisplayName: "",
        extensionDisplayName: "",
        locations: [ "panel" ],
        metadata: {  },
        slashCommands: [  ],
        disambiguation: [  ]
      },
      slashCommand: undefined,
      usedContext: {
        documents: [
          {
            uri: URI(file:///test/path/to/file),
            version: 3,
            ranges: [
              {
                startLineNumber: 1,
                startColumn: 1,
                endLineNumber: 2,
                endColumn: 2
              }
            ]
          }
        ],
        kind: "usedContext"
      },
      contentReferences: [  ],
      codeCitations: [  ],
      timestamp: undefined
    }
  ]
}